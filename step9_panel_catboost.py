"""
步骤 9：Panel 多区域 CatBoost
思路：
- 将三区域数据合并，使用 `区域_code` 作为类别特征；时间特征为年份。
- 使用与 step8 相同的高信息量特征集。
- 仅尝试 CatBoost 单模型（Ordered Boosting 时间顺序友好）。
- 通过 RandomizedSearchCV 搜索 30 组参数，评估指标 neg MAPE。
- 按区域计算测试集（2016-2022）MAPE、R²、RMSE。
- 结果保存 `metrics_step9.csv`。
"""
import warnings
warnings.filterwarnings('ignore')

from pathlib import Path
import pandas as pd
import numpy as np
from sklearn.metrics import mean_absolute_percentage_error, r2_score, mean_squared_error
from sklearn.model_selection import TimeSeriesSplit, RandomizedSearchCV
from catboost import CatBoostRegressor

PROJECT_DIR = Path(__file__).resolve().parent
DATA_DIR = PROJECT_DIR / '数据集'
CSV_PATH = DATA_DIR / '训练预测集.csv'
OUT_CSV = PROJECT_DIR / 'metrics_step9.csv'

np.random.seed(42)

def compute_metrics(y_true,y_pred):
    return (mean_absolute_percentage_error(y_true,y_pred)*100,
            r2_score(y_true,y_pred),
            np.sqrt(mean_squared_error(y_true,y_pred)))

def gen_features():
    try:
        df=pd.read_csv(CSV_PATH,encoding='utf-8')
    except UnicodeDecodeError:
        df=pd.read_csv(CSV_PATH,encoding='gbk')
    df=df.loc[:,~df.columns.str.contains('Unnamed')]
    df['年份']=df['年份'].astype(int)
    for col in ['Y','X','EI6','EI9']:
        df[col]=pd.to_numeric(df[col],errors='coerce')
    df.dropna(subset=['Y','X','EI9'],inplace=True)

    df['区域_code']=df['区域'].map({'J区':0,'H区':1,'L区':2})

    for lag in [1,2,3]:
        for col in ['X','EI9','Y']:
            df[f'{col}_lag{lag}']=df.groupby('区域')[col].shift(lag)
    for col in ['X','EI9','Y']:
        df[f'{col}_roll_mean3']=df.groupby('区域')[col].transform(lambda s:s.rolling(3,min_periods=1).mean())
        df[f'{col}_roll_std3']=df.groupby('区域')[col].transform(lambda s:s.rolling(3,min_periods=1).std().fillna(0))
    df['X_EI9']=df['X']*df['EI9']
    df['Y_pct']=df.groupby('区域')['Y'].pct_change()

    df.replace([np.inf,-np.inf],np.nan,inplace=True)
    df.fillna(method='ffill',inplace=True)
    df.fillna(method='bfill',inplace=True)

    return df.reset_index(drop=True)

def main():
    df=gen_features()
    train=df[df['年份']<=2015].reset_index(drop=True)
    test=df[df['年份']>2015].reset_index(drop=True)

    feature_cols=[c for c in df.columns if c not in ['区域','Y']]
    train[feature_cols]=train[feature_cols].apply(pd.to_numeric,errors='coerce').fillna(0)
    test[feature_cols]=test[feature_cols].apply(pd.to_numeric,errors='coerce').fillna(0)

    X_train=train[feature_cols]
    y_train=train['Y']

    tscv=TimeSeriesSplit(n_splits=3)
    cb=CatBoostRegressor(loss_function='RMSE',random_state=42,eval_metric='MAPE',verbose=0,boosting_type='Ordered')
    param_grid={
        'depth':[4,5,6,7],
        'learning_rate':[0.02,0.05,0.1],
        'iterations':[600,800,1000,1200],
        'l2_leaf_reg':[1,3,5]
    }
    cb_cv=RandomizedSearchCV(cb,param_grid,n_iter=30,cv=tscv,scoring='neg_mean_absolute_percentage_error',n_jobs=-1)
    cat_idx=[feature_cols.index('区域_code')] if '区域_code' in feature_cols else []
    cb_cv.fit(X_train,y_train,cat_features=cat_idx)
    best_model=cb_cv.best_estimator_

    # 评估
    records=[]
    for region in ['J区','H区','L区']:
        idx=test['区域']==region
        y_true=test.loc[idx,'Y']
        X_region=test.loc[idx,feature_cols]
        pred=best_model.predict(X_region)
        mape,r2,rmse=compute_metrics(y_true,pred)
        records.append([region,'Panel_CatBoost',mape,r2,rmse])

    res=pd.DataFrame(records,columns=['区域','模型','MAPE(%)','R²','RMSE'])
    res.to_csv(OUT_CSV,index=False,encoding='utf-8-sig')

    print('===== Panel CatBoost 结果 =====')
    print(res)
    ok=res[(res['MAPE(%)']<10)&(res['R²']>0.6)]
    print('\n满足严格标准的模型:')
    print(ok if not ok.empty else '暂无')

if __name__=='__main__':
    main()
