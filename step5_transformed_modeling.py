"""
步骤 5：对 Y 做对数 / 差分变换后重新建模
目标：在严格标准 (MAPE<10%, R²>0.6) 下获得合格模型。
方法：
 1. 对 Y 取对数 (logY = log(Y))
 2. 对 Y 取一阶差分 (dY = Y - Y_{t-1})
 3. 使用 XGBoost 与 RandomForest 进行 RandomizedSearch
 4. 预测后将 logY 或 dY 反变换回原尺度，再计算指标
输出： metrics_step5.csv
"""
import warnings
warnings.filterwarnings('ignore')

from pathlib import Path
import pandas as pd
import numpy as np
from sklearn.metrics import mean_absolute_percentage_error, r2_score, mean_squared_error
from sklearn.model_selection import RandomizedSearchCV, TimeSeriesSplit
from sklearn.ensemble import RandomForestRegressor
from xgboost import XGBRegressor

PROJECT_DIR = Path(__file__).resolve().parent
DATA_DIR = PROJECT_DIR / '数据集'
MAIN_CSV = DATA_DIR / '训练预测集.csv'
OUT_CSV = PROJECT_DIR / 'metrics_step5.csv'

np.random.seed(42)

def compute_metrics(y_true, y_pred):
    mape = mean_absolute_percentage_error(y_true, y_pred) * 100
    r2 = r2_score(y_true, y_pred)
    rmse = np.sqrt(mean_squared_error(y_true, y_pred))
    return mape, r2, rmse


def load_df():
    try:
        df = pd.read_csv(MAIN_CSV, encoding='utf-8')
    except UnicodeDecodeError:
        df = pd.read_csv(MAIN_CSV, encoding='gbk')
    df = df.loc[:, ~df.columns.str.contains('Unnamed')]
    df['年份'] = df['年份'].astype(int)
    for col in ['Y', 'X', 'EI9']:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    df.dropna(subset=['Y', 'X', 'EI9'], inplace=True)
    return df


def create_features(df):
    df = df.copy()
    df['X_lag1'] = df['X'].shift(1)
    df['EI9_lag1'] = df['EI9'].shift(1)
    df['Y_lag1'] = df['Y'].shift(1)
    df.fillna(method='ffill', inplace=True)
    return df.dropna().reset_index(drop=True)


def model_search(X_train, y_train):
    tscv = TimeSeriesSplit(n_splits=3)
    # RandomForest
    rf = RandomForestRegressor(random_state=42)
    rf_param = {
        'n_estimators': [100, 300, 600],
        'max_depth': [None, 5, 10, 15],
        'min_samples_leaf': [1, 2, 4]
    }
    rf_cv = RandomizedSearchCV(rf, rf_param, n_iter=20, cv=tscv, scoring='neg_mean_absolute_percentage_error', n_jobs=-1)
    rf_cv.fit(X_train, y_train)

    # XGBoost
    xgb = XGBRegressor(objective='reg:squarederror', random_state=42)
    xgb_param = {
        'n_estimators': [200, 400, 600, 800],
        'learning_rate': [0.01, 0.05, 0.1, 0.2],
        'max_depth': [3, 4, 5, 6],
        'subsample': [0.6, 0.8, 1.0]
    }
    xgb_cv = RandomizedSearchCV(xgb, xgb_param, n_iter=25, cv=tscv, scoring='neg_mean_absolute_percentage_error', n_jobs=-1)
    xgb_cv.fit(X_train, y_train)

    return [('RandomForest', rf_cv.best_estimator_, rf_cv.best_params_),
            ('XGBoost', xgb_cv.best_estimator_, xgb_cv.best_params_)]



def process_region(region_df, region_name):
    region_df = create_features(region_df)
    train = region_df[region_df['年份'] <= 2015].reset_index(drop=True)
    test = region_df[region_df['年份'] > 2015].reset_index(drop=True)
    if len(train) < 10 or len(test) < 3:
        return []

    X_cols = ['年份', 'X', 'EI9', 'X_lag1', 'EI9_lag1', 'Y_lag1']
    X_train = train[X_cols]
    X_test = test[X_cols]

    res = []

    # ---------- log(Y) 模型 ----------
    y_train_log = np.log(train['Y'])
    models = model_search(X_train, y_train_log)
    for name, best_model, params in models:
        pred_log = best_model.predict(X_test)
        pred = np.exp(pred_log)  # 反变换
        mape, r2, rmse = compute_metrics(test['Y'], pred)
        res.append([region_name, 'logY', name, params, mape, r2, rmse])

    # ---------- 差分 dY 模型 ----------
    train['dY'] = train['Y'].diff().fillna(0)
    test['dY'] = test['Y'].diff().fillna(0)
    y_train_d = train['dY']

    models_d = model_search(X_train, y_train_d)
    last_train_Y = train['Y'].iloc[-1]
    for name, best_model, params in models_d:
        pred_d = best_model.predict(X_test)
        # 将差分预测还原
        pred = np.cumsum(pred_d) + last_train_Y
        mape, r2, rmse = compute_metrics(test['Y'], pred)
        res.append([region_name, 'diffY', name, params, mape, r2, rmse])

    return res


def main():
    df = load_df()
    regions = ['J区', 'H区', 'L区']
    records = []
    for r in regions:
        rec = process_region(df[df['区域'] == r].copy(), r)
        records.extend(rec)

    res_df = pd.DataFrame(records, columns=['区域', '变换', '模型', '最佳参数', 'MAPE(%)', 'R²', 'RMSE'])
    res_df.to_csv(OUT_CSV, index=False, encoding='utf-8-sig')

    print("===== Y 变换模型结果 =====")
    print(res_df)
    ok = res_df[(res_df['MAPE(%)'] < 10) & (res_df['R²'] > 0.6)]
    print("\n满足严格标准的模型:")
    print(ok if not ok.empty else '暂无')


if __name__ == '__main__':
    main()
