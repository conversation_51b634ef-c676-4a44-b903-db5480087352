"""
步骤 10：SARIMAX + 外生变量深度调优
目标：回归统计模型，利用高信息量特征，寻找能突破 R² 瓶颈的单模型。
流程：
1.  为 J、H、L 每个区域独立建模。
2.  使用 step8 的高信息量特征集作为外生变量 (exog)。
3.  对非季节性 ARIMA 参数 (p,d,q) 在 [0,3] 范围内进行网格搜索。
4.  基于 AIC (赤池信息量准则) 为各区选择最优模型。
5.  用最优模型预测测试集，评估 MAPE、R²、RMSE。
6.  结果保存至 `metrics_step10.csv`。
"""
import warnings
warnings.filterwarnings('ignore')

from pathlib import Path
import pandas as pd
import numpy as np
import statsmodels.api as sm
from sklearn.metrics import mean_absolute_percentage_error, r2_score, mean_squared_error
from itertools import product

PROJECT_DIR = Path(__file__).resolve().parent
DATA_DIR = PROJECT_DIR / '数据集'
CSV_PATH = DATA_DIR / '训练预测集.csv'
OUT_CSV = PROJECT_DIR / 'metrics_step10.csv'

np.random.seed(42)

def compute_metrics(y_true, y_pred):
    return (mean_absolute_percentage_error(y_true, y_pred) * 100,
            r2_score(y_true, y_pred),
            np.sqrt(mean_squared_error(y_true, y_pred)))

def load_and_generate_features():
    try:
        df = pd.read_csv(CSV_PATH, encoding='utf-8')
    except UnicodeDecodeError:
        df = pd.read_csv(CSV_PATH, encoding='gbk')

    df = df.loc[:, ~df.columns.str.contains('Unnamed')]
    df['年份'] = df['年份'].astype(int)
    for col in ['Y', 'X', 'EI6', 'EI9']:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    df.dropna(subset=['Y', 'X', 'EI9'], inplace=True)

    # Generate features
    for lag in [1, 2, 3]:
        for col in ['X', 'EI9', 'Y']:
            df[f'{col}_lag{lag}'] = df.groupby('区域')[col].shift(lag)
    for col in ['X', 'EI9', 'Y']:
        df[f'{col}_roll_mean3'] = df.groupby('区域')[col].transform(lambda s: s.rolling(3, min_periods=1).mean())
        df[f'{col}_roll_std3'] = df.groupby('区域')[col].transform(lambda s: s.rolling(3, min_periods=1).std().fillna(0))
    df['X_EI9'] = df['X'] * df['EI9']
    df['Y_pct'] = df.groupby('区域')['Y'].pct_change()

    df.replace([np.inf, -np.inf], np.nan, inplace=True)
    df.fillna(method='ffill', inplace=True)
    df.fillna(method='bfill', inplace=True)

    return df.reset_index(drop=True)

def find_best_sarimax(train_y, train_exog):
    p = d = q = range(0, 3)
    pdq = list(product(p, d, q))
    best_aic = np.inf
    best_pdq = None

    for param in pdq:
        try:
            mod = sm.tsa.SARIMAX(train_y, exog=train_exog, order=param,
                                 enforce_stationarity=False, enforce_invertibility=False)
            results = mod.fit(disp=False)
            if results.aic < best_aic:
                best_aic = results.aic
                best_pdq = param
        except Exception:
            continue
    
    print(f"Found best SARIMAX order: {best_pdq} with AIC: {best_aic:.2f}")
    # Refit with the best parameters to return the model fit object
    best_model_fit = sm.tsa.SARIMAX(train_y, exog=train_exog, order=best_pdq,
                                    enforce_stationarity=False, enforce_invertibility=False).fit(disp=False)
    return best_model_fit, best_pdq

def main():
    df = load_and_generate_features()
    train_df = df[df['年份'] <= 2015]
    test_df = df[df['年份'] > 2015]

    feature_cols = [c for c in df.columns if c not in ['区域', 'Y', '区域_code', '年份']]
    all_records = []

    for region in ['J区', 'H区', 'L区']:
        print(f"\nProcessing {region}...")
        region_train = train_df[train_df['区域'] == region]
        region_test = test_df[test_df['区域'] == region]

        # Set index to '年份' for time series modeling
        region_train = region_train.set_index('年份')
        region_test = region_test.set_index('年份')

        train_y = region_train['Y']
        train_exog = region_train[feature_cols].apply(pd.to_numeric, errors='coerce').fillna(0)
        test_y = region_test['Y']
        test_exog = region_test[feature_cols].apply(pd.to_numeric, errors='coerce').fillna(0)

        if len(train_y) < 5:
            continue

        best_model, best_params = find_best_sarimax(train_y, train_exog)
        
        if best_model is None:
            print(f"Could not find a suitable model for {region}")
            continue

        pred = best_model.get_prediction(start=test_y.index[0], end=test_y.index[-1], exog=test_exog)
        y_forecasted = pred.predicted_mean

        mape, r2, rmse = compute_metrics(test_y, y_forecasted)
        all_records.append([region, 'SARIMAX', str(best_params), mape, r2, rmse])

    res_df = pd.DataFrame(all_records, columns=['区域', '模型', '最优参数', 'MAPE(%)', 'R²', 'RMSE'])
    res_df.to_csv(OUT_CSV, index=False, encoding='utf-8-sig')

    print("\n===== SARIMAX 深度调优结果 =====")
    print(res_df)
    ok = res_df[(res_df['MAPE(%)'] < 10) & (res_df['R²'] > 0.6)]
    print("\n满足严格标准的模型:")
    print(ok if not ok.empty else '暂无')

if __name__ == '__main__':
    main()
