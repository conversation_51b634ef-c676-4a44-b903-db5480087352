"""
步骤 7：基于 CatBoost 特征重要性筛选特征后重新建模
流程：
1. 载入与 step6 相同的特征全集。
2. 在训练集上训练基线 CatBoost，获取特征重要性；选取累计贡献 >=95% 或前 10 个特征（两者取小）。
3. 用筛选后的特征重新训练 CatBoost、LightGBM；评估测试集 J/H/L 区的 MAPE、R²、RMSE。
4. 结果保存 metrics_step7.csv 并打印合格模型列表。
"""
import warnings
warnings.filterwarnings('ignore')

from pathlib import Path
import pandas as pd
import numpy as np
from sklearn.metrics import mean_absolute_percentage_error, r2_score, mean_squared_error
from sklearn.model_selection import TimeSeriesSplit, RandomizedSearchCV
from lightgbm import LGBMRegressor
from catboost import CatBoostRegressor

PROJECT_DIR = Path(__file__).resolve().parent
DATA_DIR = PROJECT_DIR / '数据集'
CSV_PATH = DATA_DIR / '训练预测集.csv'
OUT_CSV = PROJECT_DIR / 'metrics_step7.csv'

np.random.seed(42)


def compute_metrics(y_true, y_pred):
    mape = mean_absolute_percentage_error(y_true, y_pred) * 100
    r2 = r2_score(y_true, y_pred)
    rmse = np.sqrt(mean_squared_error(y_true, y_pred))
    return mape, r2, rmse


def load_feature_df():
    try:
        df = pd.read_csv(CSV_PATH, encoding='utf-8')
    except UnicodeDecodeError:
        df = pd.read_csv(CSV_PATH, encoding='gbk')

    df = df.loc[:, ~df.columns.str.contains('Unnamed')]
    df['年份'] = df['年份'].astype(int)
    for col in ['Y', 'X', 'EI6', 'EI9']:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    df.dropna(subset=['Y', 'X', 'EI9'], inplace=True)

    df['区域_code'] = df['区域'].map({'J区': 0, 'H区': 1, 'L区': 2})

    for col in ['X', 'EI6', 'EI9', 'Y']:
        df[f'{col}_lag1'] = df.groupby('区域')[col].shift(1)
        df[f'{col}_lag2'] = df.groupby('区域')[col].shift(2)
    df['Y_pct'] = df.groupby('区域')['Y'].pct_change()

    df.replace([np.inf, -np.inf], np.nan, inplace=True)
    df.fillna(method='ffill', inplace=True)
    df.fillna(method='bfill', inplace=True)

    return df.reset_index(drop=True)


def select_important_features(train_df, feature_cols):
    X_train = train_df[feature_cols].apply(pd.to_numeric, errors='coerce').fillna(0)
    y_train = train_df['Y']
    # 基线 CatBoost
    model = CatBoostRegressor(verbose=0, random_state=42, depth=6, iterations=500, learning_rate=0.05)
    model.fit(X_train, y_train)
    importances = model.get_feature_importance(prettified=True)
    importances.sort_values('Importances', ascending=False, inplace=True)
    # 累计贡献 >=95% 或前10
    importances['cum'] = importances['Importances'].cumsum()
    total = importances['Importances'].sum()
    importances['cum_pct'] = importances['cum'] / total
    top_feats = importances[importances['cum_pct'] <= 0.95]['Feature Id'].tolist()
    if len(top_feats) > 10:
        top_feats = importances.head(10)['Feature Id'].tolist()
    print(f"选出的核心特征 ({len(top_feats)}):", top_feats)
    return top_feats


def model_search(X_train, y_train):
    tscv = TimeSeriesSplit(n_splits=3)
    models = []

    # CatBoost
    cb = CatBoostRegressor(verbose=0, random_state=42)
    cb_params = {
        'depth': [4, 5, 6],
        'learning_rate': [0.02, 0.05, 0.1],
        'iterations': [300, 500, 800]
    }
    cb_cv = RandomizedSearchCV(cb, cb_params, n_iter=15, cv=tscv,
                               scoring='neg_mean_absolute_percentage_error', n_jobs=-1)
    cb_cv.fit(X_train, y_train)
    models.append(('CatBoost', cb_cv.best_estimator_))

    # LightGBM
    lgb = LGBMRegressor(random_state=42)
    lgb_params = {
        'n_estimators': [300, 500, 800],
        'learning_rate': [0.02, 0.05, 0.1],
        'max_depth': [-1, 3, 5],
        'num_leaves': [15, 31, 63]
    }
    lgb_cv = RandomizedSearchCV(lgb, lgb_params, n_iter=15, cv=tscv,
                                scoring='neg_mean_absolute_percentage_error', n_jobs=-1)
    lgb_cv.fit(X_train, y_train)
    models.append(('LightGBM', lgb_cv.best_estimator_))

    return models


def evaluate(models, test_df, features):
    records = []
    for region in ['J区', 'H区', 'L区']:
        idx = test_df['区域'] == region
        y_true = test_df.loc[idx, 'Y']
        X_region = test_df.loc[idx, features]
        for name, model in models:
            pred = model.predict(X_region)
            mape, r2, rmse = compute_metrics(y_true, pred)
            records.append([region, name, mape, r2, rmse])
    return records


def main():
    df = load_feature_df()
    train_df = df[df['年份'] <= 2015].reset_index(drop=True)
    test_df = df[df['年份'] > 2015].reset_index(drop=True)

    feature_cols = [c for c in df.columns if c not in ['区域', 'Y']]
    core_features = select_important_features(train_df, feature_cols)

    # 转成数值并填缺
    train_df[core_features] = train_df[core_features].apply(pd.to_numeric, errors='coerce').fillna(0)
    test_df[core_features] = test_df[core_features].apply(pd.to_numeric, errors='coerce').fillna(0)

    X_train = train_df[core_features]
    y_train = train_df['Y']

    models = model_search(X_train, y_train)

    records = evaluate(models, test_df, core_features)
    res_df = pd.DataFrame(records, columns=['区域', '模型', 'MAPE(%)', 'R²', 'RMSE'])
    res_df.to_csv(OUT_CSV, index=False, encoding='utf-8-sig')

    print("===== 特征筛选后模型结果 =====")
    print(res_df)
    ok = res_df[(res_df['MAPE(%)'] < 10) & (res_df['R²'] > 0.6)]
    print("\n满足严格标准的模型:")
    print(ok if not ok.empty else '暂无')


if __name__ == '__main__':
    main()
