import pandas as pd
from pathlib import Path

"""
步骤 1：数据读取与初步检查
执行方式：
    python step1_data_inspection.py
输出：
    - 在终端打印每个数据集的前几行、info、缺失值统计及描述统计信息。
"""

PROJECT_DIR = Path(__file__).resolve().parent
DATA_DIR = PROJECT_DIR / '数据集'

MAIN_CSV = DATA_DIR / '训练预测集.csv'
EI_CSV = DATA_DIR / 'EI(1990-2022).csv'

def inspect_df(df: pd.DataFrame, name: str):
    print(f"\n==== {name} ====")
    print("前五行:\n", df.head())
    print("\n信息(info):")
    print(df.info())
    print("\n缺失值统计:")
    print(df.isna().sum())
    print("\n描述性统计:")
    print(df.describe(include='all'))


def main():
    # 尝试使用常见编码自动读取
    for path, label in [(MAIN_CSV, '训练预测集.csv'), (EI_CSV, 'EI(1990-2022).csv')]:
        try:
            df = pd.read_csv(path, encoding='utf-8')
        except UnicodeDecodeError:
            df = pd.read_csv(path, encoding='gbk')
        inspect_df(df, label)


if __name__ == '__main__':
    main()
