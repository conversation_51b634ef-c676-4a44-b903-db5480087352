﻿区域,模型,最佳参数,<PERSON><PERSON><PERSON>(%),<PERSON>²,<PERSON><PERSON><PERSON>区,<PERSON><PERSON><PERSON><PERSON>,"{'learning_rate': 0.05, 'l2_leaf_reg': 5, 'iterations': 500, 'depth': 4}",5.259354527794453,-7.976154911189395,43.86456082667838
J区,<PERSON><PERSON><PERSON>,"{'subsample': 0.6, 'num_leaves': 31, 'n_estimators': 300, 'max_depth': 3, 'learning_rate': 0.02}",89.05155621454844,-1844.6596967810722,628.9909986301393
J区,<PERSON><PERSON><PERSON><PERSON>,"{'subsample': 0.8, 'n_estimators': 800, 'max_depth': 3, 'learning_rate': 0.1}",9.309164759546487,-19.541759808785375,66.35710635345816
J区,<PERSON><PERSON><PERSON><PERSON>,"{'n_estimators': 300, 'min_samples_leaf': 1, 'max_depth': None}",11.125646819608518,-26.562922119416413,76.86546255738273
H区,<PERSON><PERSON><PERSON><PERSON>,"{'learning_rate': 0.05, 'l2_leaf_reg': 5, 'iterations': 500, 'depth': 4}",22.19550882534154,-9.482541569314082,667.644653137594
H区,LightGBM,"{'subsample': 0.6, 'num_leaves': 31, 'n_estimators': 300, 'max_depth': 3, 'learning_rate': 0.02}",38.03971189038232,-25.464018254030325,1060.8157823461866
H区,XGBoost,"{'subsample': 0.8, 'n_estimators': 800, 'max_depth': 3, 'learning_rate': 0.1}",17.086642196249542,-5.314473989544068,518.1800253978494
H区,RandomForest,"{'n_estimators': 300, 'min_samples_leaf': 1, 'max_depth': None}",18.667304307797203,-6.391238845140097,560.622723562898
L区,CatBoost,"{'learning_rate': 0.05, 'l2_leaf_reg': 5, 'iterations': 500, 'depth': 4}",7.216959587619664,-1.3834398899253766,33.14526936207084
L区,LightGBM,"{'subsample': 0.6, 'num_leaves': 31, 'n_estimators': 300, 'max_depth': 3, 'learning_rate': 0.02}",53.06360431805868,-116.3032407372888,232.527571690627
L区,XGBoost,"{'subsample': 0.8, 'n_estimators': 800, 'max_depth': 3, 'learning_rate': 0.1}",11.278185978542336,-5.330461786858144,54.01783161741195
L区,RandomForest,"{'n_estimators': 300, 'min_samples_leaf': 1, 'max_depth': None}",5.144251694573243,-0.3878442178003001,25.29238658053139
