"""
步骤 4：进一步精细调优当前表现较好的模型
目标：尽量让至少 3 个模型满足 MAPE<10% 且 R²>0.6。
方法：
  1. 对此前 metrics_step3.csv 中 MAPE 最低的模型做更大范围参数搜索。
  2. 仅针对变量 Y（核心指标）以及表现相对较好的组合，避免搜索爆炸。
"""
import warnings
warnings.filterwarnings('ignore')

from pathlib import Path
import pandas as pd
import numpy as np
from itertools import product
from sklearn.metrics import mean_absolute_percentage_error, r2_score, mean_squared_error
from sklearn.model_selection import TimeSeriesSplit, RandomizedSearchCV
from sklearn.ensemble import RandomForestRegressor
from sklearn.svm import SVR
from xgboost import XGBRegressor
from statsmodels.tsa.statespace.sarimax import SARIMAX

PROJECT_DIR = Path(__file__).resolve().parent
DATA_DIR = PROJECT_DIR / '数据集'
MAIN_CSV = DATA_DIR / '训练预测集.csv'
OUTPUT_CSV = PROJECT_DIR / 'metrics_step4.csv'


def compute_metrics(y_true, y_pred):
    mape = mean_absolute_percentage_error(y_true, y_pred) * 100
    r2 = r2_score(y_true, y_pred)
    rmse = np.sqrt(mean_squared_error(y_true, y_pred))
    return mape, r2, rmse


def load_data():
    try:
        df = pd.read_csv(MAIN_CSV, encoding='utf-8')
    except UnicodeDecodeError:
        df = pd.read_csv(MAIN_CSV, encoding='gbk')
    df = df.loc[:, ~df.columns.str.contains('Unnamed')]
    df['年份'] = df['年份'].astype(int)
    for col in ['Y', 'X', 'EI9']:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    df.dropna(subset=['Y', 'X', 'EI9'], inplace=True)
    return df


def create_features(df):
    """增加滞后特征提升解释力，并向前填补首行缺失值"""
    df = df.copy()
    df['X_lag1'] = df['X'].shift(1)
    df['EI9_lag1'] = df['EI9'].shift(1)
    df['Y_lag1'] = df['Y'].shift(1)
    # 使用前向填充，以免因个别缺失导致整行被删
    df.fillna(method='ffill', inplace=True)
    return df.dropna().reset_index(drop=True)


def fine_tune_region(region_df):
    region_df = create_features(region_df)
    # 训练集 / 测试集
    train = region_df[region_df['年份'] <= 2015]
    test = region_df[region_df['年份'] > 2015]

    # 若样本过少直接跳过
    if len(train) < 10 or len(test) < 3:
        return []

    X_cols = ['年份', 'X', 'EI9', 'X_lag1', 'EI9_lag1', 'Y_lag1']
    X_train, y_train = train[X_cols], train['Y']
    X_test, y_test = test[X_cols], test['Y']

    results = []

    # 1. RandomForest: RandomizedSearchCV
    rf = RandomForestRegressor(random_state=42)
    rf_param = {
        'n_estimators': [100, 200, 400, 600, 800],
        'max_depth': [None, 5, 10, 15],
        'min_samples_leaf': [1, 2, 4],
        'max_features': ['auto', 'sqrt', 0.8]
    }
    tscv = TimeSeriesSplit(n_splits=3)
    rf_cv = RandomizedSearchCV(rf, rf_param, n_iter=20, cv=tscv, scoring='neg_mean_absolute_percentage_error', n_jobs=-1)
    rf_cv.fit(X_train, y_train)
    pred = rf_cv.best_estimator_.predict(X_test)
    metrics = compute_metrics(y_test, pred)
    results.append(['RandomForest', rf_cv.best_params_, *metrics])

    # 2. XGBoost
    xgb = XGBRegressor(objective='reg:squarederror', random_state=42)
    xgb_param = {
        'n_estimators': [200, 400, 600, 800],
        'learning_rate': [0.01, 0.05, 0.1, 0.2],
        'max_depth': [3, 4, 5, 6],
        'subsample': [0.6, 0.8, 1.0],
        'colsample_bytree': [0.6, 0.8, 1.0]
    }
    xgb_cv = RandomizedSearchCV(xgb, xgb_param, n_iter=25, cv=tscv, scoring='neg_mean_absolute_percentage_error', n_jobs=-1)
    xgb_cv.fit(X_train, y_train)
    pred = xgb_cv.best_estimator_.predict(X_test)
    metrics = compute_metrics(y_test, pred)
    results.append(['XGBoost', xgb_cv.best_params_, *metrics])

    # 3. SVR
    svr = SVR()
    svr_param = {
        'C': [1, 10, 100, 500],
        'gamma': ['scale', 'auto', 0.001, 0.01],
        'epsilon': [0.05, 0.1, 0.2]
    }
    svr_cv = RandomizedSearchCV(svr, svr_param, n_iter=20, cv=tscv, scoring='neg_mean_absolute_percentage_error', n_jobs=-1)
    svr_cv.fit(X_train, y_train)
    pred = svr_cv.best_estimator_.predict(X_test)
    metrics = compute_metrics(y_test, pred)
    results.append(['SVR', svr_cv.best_params_, *metrics])

    # 4. SARIMAX (季节 + 协变量)
    best = None
    for p, d, q in product([0,1,2], [0,1], [0,1,2]):
        try:
            model = SARIMAX(train['Y'], order=(p,d,q), exog=train[['X','EI9']], enforce_stationarity=False, enforce_invertibility=False).fit(disp=False)
            pred = model.forecast(steps=len(test), exog=test[['X','EI9']])
            metrics_curr = compute_metrics(y_test.values, pred)
            if (best is None) or (metrics_curr[0] < best['MAPE(%)']):
                best = {'params': {'p':p,'d':d,'q':q}, 'metrics': metrics_curr}
        except Exception:
            continue
    if best:
        results.append(['SARIMAX', best['params'], *best['metrics']])

    return results


def main():
    df = load_data()
    regions = ['J区', 'H区', 'L区']
    all_res = []
    for region in regions:
        reg_df = df[df['区域'] == region].copy()
        reg_df.sort_values('年份', inplace=True)
        res = fine_tune_region(reg_df)
        for r in res:
            all_res.append([region, *r])

    res_df = pd.DataFrame(all_res, columns=['区域', '模型', '最佳参数', 'MAPE(%)', 'R²', 'RMSE'])
    res_df.to_csv(OUTPUT_CSV, index=False, encoding='utf-8-sig')

    print("===== 精细调优结果 =====")
    print(res_df)
    ok = res_df[(res_df['MAPE(%)'] < 10) & (res_df['R²'] > 0.6)]
    print("\n满足 MAPE<10% 且 R²>0.6 的模型:")
    print(ok if not ok.empty else '暂无')


if __name__ == '__main__':
    main()
