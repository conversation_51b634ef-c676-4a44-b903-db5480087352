"""
步骤 8：高信息量特征 + CatBoost / LightGBM
1. 新增特征：
   - lag1~lag3（X/EI9/Y）
   - 3 年滚动均值 & 滚动标准差（X, EI9, Y）
   - 交互项：X*EI9, lag1*lag2
   - Y 的同比增速（pct）
2. 多区域合并训练，区域编码保留。
3. CatBoost、LightGBM 随机搜索 30 次，评分 neg MAPE。
4. 按区域评估，结果保存 metrics_step8.csv。
"""
import warnings
warnings.filterwarnings('ignore')

from pathlib import Path
import pandas as pd
import numpy as np
from sklearn.metrics import mean_absolute_percentage_error, r2_score, mean_squared_error
from sklearn.model_selection import TimeSeriesSplit, RandomizedSearchCV
from lightgbm import LGBMRegressor
from catboost import CatBoostRegressor

PROJECT_DIR = Path(__file__).resolve().parent
DATA_DIR = PROJECT_DIR / '数据集'
CSV_PATH = DATA_DIR / '训练预测集.csv'
OUT_CSV = PROJECT_DIR / 'metrics_step8.csv'

np.random.seed(42)

def compute_metrics(y_true, y_pred):
    return (mean_absolute_percentage_error(y_true, y_pred) * 100,
            r2_score(y_true, y_pred),
            np.sqrt(mean_squared_error(y_true, y_pred)))

def load_and_generate():
    try:
        df = pd.read_csv(CSV_PATH, encoding='utf-8')
    except UnicodeDecodeError:
        df = pd.read_csv(CSV_PATH, encoding='gbk')

    df = df.loc[:, ~df.columns.str.contains('Unnamed')]
    df['年份'] = df['年份'].astype(int)
    for col in ['Y', 'X', 'EI6', 'EI9']:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    df.dropna(subset=['Y', 'X', 'EI9'], inplace=True)

    df['区域_code'] = df['区域'].map({'J区':0,'H区':1,'L区':2})

    # lag 1-3
    for lag in [1,2,3]:
        for col in ['X','EI9','Y']:
            df[f'{col}_lag{lag}'] = df.groupby('区域')[col].shift(lag)
    # rolling mean & std (window=3)
    for col in ['X','EI9','Y']:
        df[f'{col}_roll_mean3'] = df.groupby('区域')[col].transform(lambda s: s.rolling(3, min_periods=1).mean())
        df[f'{col}_roll_std3'] = df.groupby('区域')[col].transform(lambda s: s.rolling(3, min_periods=1).std().fillna(0))
    # interactions
    df['X_EI9'] = df['X'] * df['EI9']
    df['lag1_lag2'] = df['Y_lag1'] * df['Y_lag2'] if 'Y_lag1' in df and 'Y_lag2' in df else np.nan
    # pct change
    df['Y_pct'] = df.groupby('区域')['Y'].pct_change()

    df.replace([np.inf, -np.inf], np.nan, inplace=True)
    df.fillna(method='ffill', inplace=True)
    df.fillna(method='bfill', inplace=True)

    return df.reset_index(drop=True)

def model_search(X, y):
    tscv = TimeSeriesSplit(n_splits=3)

    models=[]
    cb = CatBoostRegressor(verbose=0, random_state=42)
    cb_params={
        'depth':[4,5,6,7],
        'learning_rate':[0.02,0.05,0.1],
        'iterations':[400,600,800,1000],
        'l2_leaf_reg':[1,3,5]
    }
    cb_cv=RandomizedSearchCV(cb,cb_params,n_iter=30,cv=tscv,scoring='neg_mean_absolute_percentage_error',n_jobs=-1)
    cb_cv.fit(X,y)
    models.append(('CatBoost',cb_cv.best_estimator_))

    lgb=LGBMRegressor(random_state=42)
    lgb_params={
        'n_estimators':[400,600,800,1000],
        'learning_rate':[0.02,0.05,0.1],
        'max_depth':[-1,3,5,7],
        'num_leaves':[15,31,63,127]
    }
    lgb_cv=RandomizedSearchCV(lgb,lgb_params,n_iter=30,cv=tscv,scoring='neg_mean_absolute_percentage_error',n_jobs=-1)
    lgb_cv.fit(X,y)
    models.append(('LightGBM',lgb_cv.best_estimator_))
    return models

def evaluate(models, test_df, features):
    records=[]
    for region in ['J区','H区','L区']:
        idx=test_df['区域']==region
        y_true=test_df.loc[idx,'Y']
        X_region=test_df.loc[idx,features]
        for name,model in models:
            pred=model.predict(X_region)
            mape,r2,rmse=compute_metrics(y_true,pred)
            records.append([region,name,mape,r2,rmse])
    return records

def main():
    df=load_and_generate()
    train_df=df[df['年份']<=2015].reset_index(drop=True)
    test_df=df[df['年份']>2015].reset_index(drop=True)

    feature_cols=[c for c in df.columns if c not in ['区域','Y']]
    # ensure numeric
    train_df[feature_cols]=train_df[feature_cols].apply(pd.to_numeric, errors='coerce').fillna(0)
    test_df[feature_cols]=test_df[feature_cols].apply(pd.to_numeric, errors='coerce').fillna(0)

    X_train=train_df[feature_cols]
    y_train=train_df['Y']

    models=model_search(X_train,y_train)
    records=evaluate(models,test_df,feature_cols)
    res=pd.DataFrame(records,columns=['区域','模型','MAPE(%)','R²','RMSE'])
    res.to_csv(OUT_CSV,index=False,encoding='utf-8-sig')

    print('===== 高信息量特征模型结果 =====')
    print(res)
    ok=res[(res['MAPE(%)']<10)&(res['R²']>0.6)]
    print('\n满足严格标准的模型:')
    print(ok if not ok.empty else '暂无')

if __name__=='__main__':
    main()
