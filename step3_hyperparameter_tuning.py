"""
步骤 3：系统性超参数调优
目标：为 X、EI9、Y 在三个区域寻找最佳参数组合，提高 MAPE 与 R²。
结果：
    - 在终端打印每个变量、模型的最佳参数与测试集指标
    - 输出 metrics_step3.csv
使用方法：
    python step3_hyperparameter_tuning.py
"""
import warnings
warnings.filterwarnings('ignore')

from pathlib import Path
import pandas as pd
import numpy as np
from itertools import product
from sklearn.metrics import mean_absolute_percentage_error, r2_score, mean_squared_error
from sklearn.ensemble import RandomForestRegressor
from sklearn.svm import SVR
from xgboost import XGBRegressor
from statsmodels.tsa.arima.model import ARIMA
try:
    from prophet import Prophet
except ImportError:
    Prophet = None

PROJECT_DIR = Path(__file__).resolve().parent
DATA_DIR = PROJECT_DIR / '数据集'
MAIN_CSV = DATA_DIR / '训练预测集.csv'
OUTPUT_CSV = PROJECT_DIR / 'metrics_step3.csv'

# --------------------------------- 工具函数 ---------------------------------

def compute_metrics(y_true, y_pred):
    mape = mean_absolute_percentage_error(y_true, y_pred) * 100
    r2 = r2_score(y_true, y_pred)
    try:
        rmse = mean_squared_error(y_true, y_pred, squared=False)
    except TypeError:
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))
    return mape, r2, rmse


def train_test_split(df: pd.DataFrame, train_end_year: int):
    train = df[df['年份'] <= train_end_year]
    test = df[df['年份'] > train_end_year]
    return train.reset_index(drop=True), test.reset_index(drop=True)


# ---------------------------- 读取数据 ------------------------------

def load_data():
    try:
        df = pd.read_csv(MAIN_CSV, encoding='utf-8')
    except UnicodeDecodeError:
        df = pd.read_csv(MAIN_CSV, encoding='gbk')
    df = df.loc[:, ~df.columns.str.contains('Unnamed')]
    df = df[df['年份'].notna()]
    df['年份'] = df['年份'].astype(int)
    for col in ['Y', 'X', 'EI9']:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    df.dropna(subset=['Y', 'X', 'EI9'], inplace=True)
    return df


def grid_search_singlevar(region_df, var, model_name):
    """对单变量时间序列模型做简单网格搜索，返回最佳指标与参数"""
    train, test = train_test_split(region_df[['年份', var]], 2015)
    y_train, y_test = train[var].values, test[var].values
    X_train_year = train['年份'].values.reshape(-1, 1)
    X_test_year = test['年份'].values.reshape(-1, 1)

    best = None

    if model_name == 'RandomForest':
        param_grid = {
            'n_estimators': [100, 300, 500],
            'max_depth': [None, 5, 10],
            'min_samples_leaf': [1, 2, 4]
        }
        for n, md, leaf in product(*param_grid.values()):
            m = RandomForestRegressor(n_estimators=n, max_depth=md, min_samples_leaf=leaf, random_state=42)
            m.fit(X_train_year, y_train)
            pred = m.predict(X_test_year)
            metrics = compute_metrics(y_test, pred)
            if (best is None) or (metrics[0] < best['MAPE(%)']):
                best = {'参数': {'n_estimators': n, 'max_depth': md, 'min_samples_leaf': leaf},
                        'MAPE(%)': metrics[0], 'R²': metrics[1], 'RMSE': metrics[2]}

    elif model_name == 'SVR':
        param_grid = {
            'C': [1, 10, 100],
            'gamma': ['scale', 'auto', 0.01],
            'epsilon': [0.1, 0.2]
        }
        for C, gamma, eps in product(*param_grid.values()):
            m = SVR(C=C, gamma=gamma, epsilon=eps)
            m.fit(X_train_year, y_train)
            pred = m.predict(X_test_year)
            metrics = compute_metrics(y_test, pred)
            if (best is None) or (metrics[0] < best['MAPE(%)']):
                best = {'参数': {'C': C, 'gamma': gamma, 'epsilon': eps},
                        'MAPE(%)': metrics[0], 'R²': metrics[1], 'RMSE': metrics[2]}

    elif model_name == 'XGBoost':
        param_grid = {
            'n_estimators': [100, 300],
            'learning_rate': [0.05, 0.1, 0.3],
            'max_depth': [3, 4, 5]
        }
        for n, lr, md in product(*param_grid.values()):
            m = XGBRegressor(objective='reg:squarederror', n_estimators=n, learning_rate=lr,
                              max_depth=md, random_state=42)
            m.fit(X_train_year, y_train)
            pred = m.predict(X_test_year)
            metrics = compute_metrics(y_test, pred)
            if (best is None) or (metrics[0] < best['MAPE(%)']):
                best = {'参数': {'n_estimators': n, 'learning_rate': lr, 'max_depth': md},
                        'MAPE(%)': metrics[0], 'R²': metrics[1], 'RMSE': metrics[2]}

    elif model_name == 'ARIMA':
        best = None
        for p, d, q in product([0, 1, 2], [0, 1], [0, 1, 2]):
            try:
                model = ARIMA(y_train, order=(p, d, q)).fit()
                pred = model.forecast(steps=len(y_test))
                metrics = compute_metrics(y_test, pred)
                if (best is None) or (metrics[0] < best['MAPE(%)']):
                    best = {'参数': {'p': p, 'd': d, 'q': q},
                            'MAPE(%)': metrics[0], 'R²': metrics[1], 'RMSE': metrics[2]}
            except Exception:
                continue

    elif model_name == 'Prophet' and Prophet is not None:
        best = None
        param_grid = {'changepoint_prior_scale': [0.05, 0.1, 0.5]}
        for cps in param_grid['changepoint_prior_scale']:
            df_prophet_train = pd.DataFrame({'ds': pd.to_datetime(train['年份'], format='%Y'), 'y': y_train})
            m = Prophet(yearly_seasonality=False, daily_seasonality=False, weekly_seasonality=False,
                        changepoint_prior_scale=cps)
            m.fit(df_prophet_train)
            future = m.make_future_dataframe(periods=len(y_test), freq='Y')
            forecast = m.predict(future)
            pred = forecast['yhat'].iloc[-len(y_test):].values
            metrics = compute_metrics(y_test, pred)
            if (best is None) or (metrics[0] < best['MAPE(%)']):
                best = {'参数': {'changepoint_prior_scale': cps},
                        'MAPE(%)': metrics[0], 'R²': metrics[1], 'RMSE': metrics[2]}

    return best


def grid_search_multivar(region_df, model_name):
    """对 Y 的多元模型做网格搜索"""
    train, test = train_test_split(region_df[['年份', 'Y', 'X', 'EI9']], 2015)
    X_train = train[['年份', 'X', 'EI9']]
    y_train = train['Y']
    X_test = test[['年份', 'X', 'EI9']]
    y_test = test['Y']

    best = None

    if model_name == 'RandomForest':
        param_grid = {
            'n_estimators': [100, 300, 500],
            'max_depth': [None, 5, 10],
            'min_samples_leaf': [1, 2, 4]
        }
        for n, md, leaf in product(*param_grid.values()):
            m = RandomForestRegressor(n_estimators=n, max_depth=md, min_samples_leaf=leaf, random_state=42)
            m.fit(X_train, y_train)
            pred = m.predict(X_test)
            metrics = compute_metrics(y_test, pred)
            if (best is None) or (metrics[0] < best['MAPE(%)']):
                best = {'参数': {'n_estimators': n, 'max_depth': md, 'min_samples_leaf': leaf},
                        'MAPE(%)': metrics[0], 'R²': metrics[1], 'RMSE': metrics[2]}

    elif model_name == 'SVR':
        param_grid = {
            'C': [1, 10, 100],
            'gamma': ['scale', 'auto', 0.01],
            'epsilon': [0.1, 0.2]
        }
        for C, gamma, eps in product(*param_grid.values()):
            m = SVR(C=C, gamma=gamma, epsilon=eps)
            m.fit(X_train, y_train)
            pred = m.predict(X_test)
            metrics = compute_metrics(y_test, pred)
            if (best is None) or (metrics[0] < best['MAPE(%)']):
                best = {'参数': {'C': C, 'gamma': gamma, 'epsilon': eps},
                        'MAPE(%)': metrics[0], 'R²': metrics[1], 'RMSE': metrics[2]}

    elif model_name == 'XGBoost':
        param_grid = {
            'n_estimators': [100, 300],
            'learning_rate': [0.05, 0.1, 0.3],
            'max_depth': [3, 4, 5]
        }
        for n, lr, md in product(*param_grid.values()):
            m = XGBRegressor(objective='reg:squarederror', n_estimators=n, learning_rate=lr,
                              max_depth=md, random_state=42)
            m.fit(X_train, y_train)
            pred = m.predict(X_test)
            metrics = compute_metrics(y_test, pred)
            if (best is None) or (metrics[0] < best['MAPE(%)']):
                best = {'参数': {'n_estimators': n, 'learning_rate': lr, 'max_depth': md},
                        'MAPE(%)': metrics[0], 'R²': metrics[1], 'RMSE': metrics[2]}

    elif model_name == 'ARIMA':
        # 使用纯 Y 序列，只搜索少量参数
        y_train_seq = y_train.values
        best = None
        for p, d, q in product([0, 1, 2], [0, 1], [0, 1, 2]):
            try:
                model = ARIMA(y_train_seq, order=(p, d, q)).fit()
                pred = model.forecast(steps=len(y_test))
                metrics = compute_metrics(y_test.values, pred)
                if (best is None) or (metrics[0] < best['MAPE(%)']):
                    best = {'参数': {'p': p, 'd': d, 'q': q},
                            'MAPE(%)': metrics[0], 'R²': metrics[1], 'RMSE': metrics[2]}
            except Exception:
                continue

    elif model_name == 'Prophet' and Prophet is not None:
        best = None
        param_grid = {'changepoint_prior_scale': [0.05, 0.1, 0.5]}
        for cps in param_grid['changepoint_prior_scale']:
            df_prophet_train = pd.DataFrame({'ds': pd.to_datetime(train['年份'], format='%Y'), 'y': y_train})
            m = Prophet(yearly_seasonality=False, changepoint_prior_scale=cps)
            m.fit(df_prophet_train)
            future = m.make_future_dataframe(periods=len(y_test), freq='Y')
            forecast = m.predict(future)
            pred = forecast['yhat'].iloc[-len(y_test):].values
            metrics = compute_metrics(y_test.values, pred)
            if (best is None) or (metrics[0] < best['MAPE(%)']):
                best = {'参数': {'changepoint_prior_scale': cps},
                        'MAPE(%)': metrics[0], 'R²': metrics[1], 'RMSE': metrics[2]}

    return best


# ------------------------------ 主流程 ------------------------------

def main():
    df = load_data()
    regions = ['J区', 'H区', 'L区']
    singlevar_models = ['RandomForest', 'SVR', 'XGBoost', 'ARIMA', 'Prophet']
    multivar_models = ['RandomForest', 'SVR', 'XGBoost', 'ARIMA', 'Prophet']

    results = []

    for region in regions:
        reg_df = df[df['区域'] == region].copy()
        reg_df.sort_values('年份', inplace=True)
        # 单变量 X、EI9
        for var in ['X', 'EI9']:
            for model_name in singlevar_models:
                if model_name == 'Prophet' and Prophet is None:
                    continue
                best = grid_search_singlevar(reg_df, var, model_name)
                if best is not None:
                    results.append([region, var, model_name, best['参数'], best['MAPE(%)'], best['R²'], best['RMSE']])

        # 多元 Y
        for model_name in multivar_models:
            if model_name == 'Prophet' and Prophet is None:
                continue
            best = grid_search_multivar(reg_df, model_name)
            if best is not None:
                results.append([region, 'Y', model_name, best['参数'], best['MAPE(%)'], best['R²'], best['RMSE']])

    metrics_df = pd.DataFrame(results, columns=['区域', '变量', '模型', '最佳参数', 'MAPE(%)', 'R²', 'RMSE'])
    metrics_df.to_csv(OUTPUT_CSV, index=False, encoding='utf-8-sig')

    print("\n========== 超参数调优结果汇总 ==========")
    print(metrics_df)

    ok = metrics_df[(metrics_df['MAPE(%)'] < 10) & (metrics_df['R²'] > 0.6)]
    print("\n满足 MAPE<10% 且 R²>0.6 的候选模型:")
    if ok.empty:
        print("暂未找到满足条件的模型，请考虑放宽阈值或引入更多特征/模型。")
    else:
        print(ok[['区域', '变量', '模型', '最佳参数', 'MAPE(%)', 'R²']])


if __name__ == '__main__':
    main()
