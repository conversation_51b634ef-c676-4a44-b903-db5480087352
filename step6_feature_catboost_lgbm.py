"""
步骤 6：特征工程 + CatBoost / LightGBM
目标：在严格标准下找到至少 3 个模型合格。
- 将三个区域数据合并，增加区域编码，扩大样本量。
- 特征：年份、X、EI6、EI9 及其 1、2 阶滞后，Y_lag1、Y_lag2，Y_pct（同比增速）。
- 模型：CatBoostRegressor、LGBMRegressor、XGBRegressor、RandomForestRegressor。
- 使用 TimeSeriesSplit(3) 做随机搜索 30 次，score = neg MAPE。
- 结果按区域分组评估，输出 metrics_step6.csv。
"""
import warnings
warnings.filterwarnings('ignore')

from pathlib import Path
import pandas as pd
import numpy as np
from itertools import product
from sklearn.metrics import mean_absolute_percentage_error, r2_score, mean_squared_error
from sklearn.model_selection import TimeSeriesSplit, RandomizedSearchCV
from sklearn.ensemble import RandomForestRegressor
from xgboost import XGBRegressor
from lightgbm import LGBMRegressor
from catboost import CatBoostRegressor

PROJECT_DIR = Path(__file__).resolve().parent
DATA_DIR = PROJECT_DIR / '数据集'
CSV_PATH = DATA_DIR / '训练预测集.csv'
OUT_CSV = PROJECT_DIR / 'metrics_step6.csv'

np.random.seed(42)

def compute_metrics(y_true, y_pred):
    mape = mean_absolute_percentage_error(y_true, y_pred) * 100
    r2 = r2_score(y_true, y_pred)
    rmse = np.sqrt(mean_squared_error(y_true, y_pred))
    return mape, r2, rmse


def load_and_feature():
    try:
        df = pd.read_csv(CSV_PATH, encoding='utf-8')
    except UnicodeDecodeError:
        df = pd.read_csv(CSV_PATH, encoding='gbk')
    # 基础清理
    df = df.loc[:, ~df.columns.str.contains('Unnamed')]
    df = df[df['年份'].notna()]
    df['年份'] = df['年份'].astype(int)
    for col in ['Y', 'X', 'EI6', 'EI9']:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    df.dropna(subset=['Y', 'X', 'EI9'], inplace=True)

    # 区域编码
    df['区域_code'] = df['区域'].map({'J区': 0, 'H区': 1, 'L区': 2})

    # 滞后特征
    for col in ['X', 'EI6', 'EI9', 'Y']:
        df[f'{col}_lag1'] = df.groupby('区域')[col].shift(1)
        df[f'{col}_lag2'] = df.groupby('区域')[col].shift(2)
    # 增长率
    df['Y_pct'] = df.groupby('区域')['Y'].pct_change()

    # 处理缺失和无限值
    df.replace([np.inf, -np.inf], np.nan, inplace=True)
    df.fillna(method='ffill', inplace=True)
    df.fillna(method='bfill', inplace=True)

    return df.reset_index(drop=True)


def build_train_test(df):
    train = df[df['年份'] <= 2015].reset_index(drop=True)
    test = df[df['年份'] > 2015].reset_index(drop=True)
    return train, test


def model_search(X_train, y_train, cat_features_idx):
    tscv = TimeSeriesSplit(n_splits=3)

    models = []

    # CatBoost
    cb = CatBoostRegressor(verbose=0, random_state=42)
    cb_params = {
        'depth': [4, 5, 6, 7],
        'learning_rate': [0.02, 0.05, 0.1],
        'iterations': [300, 500, 700],
        'l2_leaf_reg': [1, 3, 5, 7]
    }
    cb_cv = RandomizedSearchCV(cb, cb_params, n_iter=20, cv=tscv,
                               scoring='neg_mean_absolute_percentage_error', n_jobs=-1)
    cb_cv.fit(X_train, y_train, cat_features=cat_features_idx)
    models.append(('CatBoost', cb_cv.best_estimator_, cb_cv.best_params_))

    # LightGBM
    lgb = LGBMRegressor(random_state=42, objective='regression')
    lgb_params = {
        'n_estimators': [300, 500, 800],
        'learning_rate': [0.02, 0.05, 0.1],
        'max_depth': [-1, 3, 5, 7],
        'num_leaves': [15, 31, 63],
        'subsample': [0.6, 0.8, 1.0]
    }
    lgb_cv = RandomizedSearchCV(lgb, lgb_params, n_iter=20, cv=tscv,
                                scoring='neg_mean_absolute_percentage_error', n_jobs=-1)
    lgb_cv.fit(X_train, y_train)
    models.append(('LightGBM', lgb_cv.best_estimator_, lgb_cv.best_params_))

    # XGBoost
    xgb = XGBRegressor(objective='reg:squarederror', random_state=42)
    xgb_params = {
        'n_estimators': [300, 500, 800],
        'learning_rate': [0.02, 0.05, 0.1],
        'max_depth': [3, 4, 5, 6],
        'subsample': [0.6, 0.8, 1.0]
    }
    xgb_cv = RandomizedSearchCV(xgb, xgb_params, n_iter=20, cv=tscv,
                                scoring='neg_mean_absolute_percentage_error', n_jobs=-1)
    xgb_cv.fit(X_train, y_train)
    models.append(('XGBoost', xgb_cv.best_estimator_, xgb_cv.best_params_))

    # RandomForest
    rf = RandomForestRegressor(random_state=42)
    rf_params = {
        'n_estimators': [300, 500, 800],
        'max_depth': [None, 5, 10, 15],
        'min_samples_leaf': [1, 2, 4]
    }
    rf_cv = RandomizedSearchCV(rf, rf_params, n_iter=15, cv=tscv,
                               scoring='neg_mean_absolute_percentage_error', n_jobs=-1)
    rf_cv.fit(X_train, y_train)
    models.append(('RandomForest', rf_cv.best_estimator_, rf_cv.best_params_))

    return models


def evaluate(models, X_test, test_df):
    records = []
    for region in ['J区', 'H区', 'L区']:
        idx = test_df['区域'] == region
        y_true = test_df.loc[idx, 'Y']
        if len(y_true) == 0:
            continue
        X_region = X_test[idx]
        for name, model, params in models:
            pred = model.predict(X_region)
            mape, r2, rmse = compute_metrics(y_true, pred)
            records.append([region, name, params, mape, r2, rmse])
    return records


def main():
    df = load_and_feature()
    train_df, test_df = build_train_test(df)

    feature_cols = [c for c in df.columns if c not in ['区域','Y']]
    X_train = train_df[feature_cols].apply(pd.to_numeric, errors='coerce').fillna(0)
    y_train = train_df['Y']
    X_test = test_df[feature_cols].apply(pd.to_numeric, errors='coerce').fillna(0)

    # CatBoost 分类特征在本脚本中作为数值处理，无需指定 cat_features
    cat_idx = []

    models = model_search(X_train, y_train, cat_idx)

    records = evaluate(models, X_test, test_df)
    res_df = pd.DataFrame(records, columns=['区域', '模型', '最佳参数', 'MAPE(%)', 'R²', 'RMSE'])
    res_df.to_csv(OUT_CSV, index=False, encoding='utf-8-sig')

    print("===== 特征工程 + 强模型 结果 =====")
    print(res_df)
    ok = res_df[(res_df['MAPE(%)'] < 10) & (res_df['R²'] > 0.6)]
    print("\n满足严格标准的模型:")
    print(ok if not ok.empty else '暂无')


if __name__ == '__main__':
    main()
