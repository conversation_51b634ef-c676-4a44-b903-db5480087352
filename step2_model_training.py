"""
步骤 2：按时间序列切分训练/测试集并训练候选模型，输出评估指标。
执行方式：
    python -m pip install -r requirements.txt  # 第一次运行先安装依赖
    python step2_model_training.py

输出：
    - 终端中打印每个区域、变量、模型的 MAPE、R²、RMSE
    - 生成 metrics_step2.csv 汇总所有评估结果

说明：
    1. 本脚本只做 1990-2015 训练、2016-2022 测试 的评估，不进行 2023-2027 预测。
    2. 对 X、EI9 采用单变量建模；对 Y 使用 X 和 EI9 作为协变量（多元）
    3. 为简化实现，GM(1,1) 用 numpy 手工实现；Prophet 仅在单变量场景使用。
"""

import warnings
warnings.filterwarnings('ignore')

from pathlib import Path
import pandas as pd
import numpy as np
from sklearn.metrics import r2_score, mean_squared_error
from sklearn.metrics import mean_absolute_percentage_error
from sklearn.ensemble import RandomForestRegressor
from sklearn.svm import SVR
from xgboost import XGBRegressor
from statsmodels.tsa.arima.model import ARIMA
# prophet 统一使用新的库名 prophet
try:
    from prophet import Prophet
except ImportError:
    Prophet = None  # 如果用户未安装，可跳过 prophet

PROJECT_DIR = Path(__file__).resolve().parent
DATA_DIR = PROJECT_DIR / '数据集'
MAIN_CSV = DATA_DIR / '训练预测集.csv'

OUTPUT_CSV = PROJECT_DIR / 'metrics_step2.csv'

# --------------- 工具函数 -----------------

def gm11_forecast(series: pd.Series, pred_steps: int = 1):
    """简单 GM(1,1) 预测实现"""
    # 累加生成序列
    X1 = series.cumsum()
    # 构造数据矩阵 B 和向量 Y
    B = np.column_stack([
        -0.5 * (X1[:-1] + X1[1:]),
        np.ones(len(X1) - 1)
    ])
    Y = series.values[1:]
    # 拟合参数 [a, b]
    [[a], [b]] = np.linalg.inv(B.T @ B) @ B.T @ Y
    # 预测
    def x_hat(k):
        return (series.values[0] - b / a) * np.exp(-a * (k - 1)) + b / a
    forecast = [x_hat(k) - x_hat(k - 1) for k in range(len(series) + 1, len(series) + pred_steps + 1)]
    return pd.Series(forecast, index=range(series.index[-1] + 1, series.index[-1] + pred_steps + 1))


def train_test_split(df: pd.DataFrame, train_end_year: int):
    train = df[df['年份'] <= train_end_year]
    test = df[df['年份'] > train_end_year]
    return train.reset_index(drop=True), test.reset_index(drop=True)


def compute_metrics(y_true, y_pred):
    mape = mean_absolute_percentage_error(y_true, y_pred) * 100  # 转百分比
    r2 = r2_score(y_true, y_pred)
    # 兼容旧版 scikit-learn：若 mean_squared_error 不支持 squared 参数则回退手动计算 RMSE
    try:
        rmse = mean_squared_error(y_true, y_pred, squared=False)
    except TypeError:
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))
    return mape, r2, rmse

# ----------------------------------------

# 读取并预处理数据
try:
    df = pd.read_csv(MAIN_CSV, encoding='utf-8')
except UnicodeDecodeError:
    df = pd.read_csv(MAIN_CSV, encoding='gbk')

# 去掉无用空列
df = df.loc[:, ~df.columns.str.contains('Unnamed')]
# 只保留前 6 列（区域、年份、Y、X、EI6、EI9）
columns_keep = df.columns[:6]
df = df[columns_keep]

# 统一数据类型
for col in ['Y', 'X', 'EI6', 'EI9']:
    df[col] = pd.to_numeric(df[col], errors='coerce')

df.dropna(subset=['Y', 'X', 'EI9'], inplace=True)

df['年份'] = df['年份'].astype(int)

regions = ['J区', 'H区', 'L区']
variables = ['X', 'EI9', 'Y']  # 预测目标顺序

records = []  # 保存指标

for region in regions:
    df_reg = df[df['区域'] == region].copy()
    # 排序确保按年份
    df_reg.sort_values('年份', inplace=True)

    # -------- 变量 X 和 EI9（单变量） ---------
    for var in ['X', 'EI9']:
        train, test = train_test_split(df_reg[['年份', var]], 2015)
        y_train, y_test = train[var].values, test[var].values
        # 年份转 numeric 特征
        X_train_year = train['年份'].values.reshape(-1, 1)
        X_test_year = test['年份'].values.reshape(-1, 1)

        # 1. RandomForest
        rf = RandomForestRegressor(random_state=42)
        rf.fit(X_train_year, y_train)
        pred_rf = rf.predict(X_test_year)
        metrics = compute_metrics(y_test, pred_rf)
        records.append([region, var, 'RandomForest', *metrics])

        # 2. SVR
        svr = SVR()
        svr.fit(X_train_year, y_train)
        pred_svr = svr.predict(X_test_year)
        records.append([region, var, 'SVR', *compute_metrics(y_test, pred_svr)])

        # 3. XGBoost
        xgb = XGBRegressor(objective='reg:squarederror', random_state=42)
        xgb.fit(X_train_year, y_train)
        pred_xgb = xgb.predict(X_test_year)
        records.append([region, var, 'XGBoost', *compute_metrics(y_test, pred_xgb)])

        # 4. ARIMA（简单 grid p,d,q = (1,1,1)）
        try:
            model = ARIMA(y_train, order=(1, 1, 1)).fit()
            pred_arima = model.forecast(steps=len(y_test))
            records.append([region, var, 'ARIMA(1,1,1)', *compute_metrics(y_test, pred_arima)])
        except Exception as e:
            print(f"ARIMA 失败 {region}-{var}: {e}")

        # 5. Prophet
        if Prophet is not None:
            try:
                df_prophet_train = pd.DataFrame({
                    'ds': pd.to_datetime(train['年份'], format='%Y'),
                    'y': y_train
                })
                m = Prophet(yearly_seasonality=False, daily_seasonality=False, weekly_seasonality=False)
                m.fit(df_prophet_train)
                future = m.make_future_dataframe(periods=len(y_test), freq='Y')
                forecast = m.predict(future)
                pred_prophet = forecast['yhat'].iloc[-len(y_test):].values
                records.append([region, var, 'Prophet', *compute_metrics(y_test, pred_prophet)])
            except Exception as e:
                print(f"Prophet 失败 {region}-{var}: {e}")

        # 6. GM(1,1)
        try:
            pred_gm = gm11_forecast(pd.Series(y_train, index=train['年份']), pred_steps=len(y_test))
            records.append([region, var, 'GM(1,1)', *compute_metrics(y_test, pred_gm.values)])
        except Exception as e:
            print(f"GM(1,1) 失败 {region}-{var}: {e}")

    # -------- 变量 Y （多元：X、EI9 + 年份） ---------
    train, test = train_test_split(df_reg[['年份', 'Y', 'X', 'EI9']], 2015)
    X_train = train[['年份', 'X', 'EI9']]
    y_train = train['Y']
    X_test = test[['年份', 'X', 'EI9']]
    y_test = test['Y']

    # RandomForest
    rf = RandomForestRegressor(random_state=42)
    rf.fit(X_train, y_train)
    pred_rf = rf.predict(X_test)
    records.append([region, 'Y', 'RandomForest', *compute_metrics(y_test, pred_rf)])

    # SVR
    svr = SVR()
    svr.fit(X_train, y_train)
    pred_svr = svr.predict(X_test)
    records.append([region, 'Y', 'SVR', *compute_metrics(y_test, pred_svr)])

    # XGBoost
    xgb = XGBRegressor(objective='reg:squarederror', random_state=42)
    xgb.fit(X_train, y_train)
    pred_xgb = xgb.predict(X_test)
    records.append([region, 'Y', 'XGBoost', *compute_metrics(y_test, pred_xgb)])

    # ARIMA -> 使用纯 Y 序列
    try:
        model = ARIMA(y_train, order=(1, 1, 1)).fit()
        pred_arima = model.forecast(steps=len(y_test))
        records.append([region, 'Y', 'ARIMA(1,1,1)', *compute_metrics(y_test, pred_arima)])
    except Exception as e:
        print(f"ARIMA 失败 {region}-Y: {e}")

    # GM(1,1)
    try:
        pred_gm = gm11_forecast(pd.Series(y_train.values, index=train['年份']), pred_steps=len(y_test))
        records.append([region, 'Y', 'GM(1,1)', *compute_metrics(y_test, pred_gm.values)])
    except Exception as e:
        print(f"GM(1,1) 失败 {region}-Y: {e}")

# ---------------- 保存并打印 -----------------
metrics_df = pd.DataFrame(records, columns=['区域', '变量', '模型', 'MAPE(%)', 'R²', 'RMSE'])
metrics_df.to_csv(OUTPUT_CSV, index=False, encoding='utf-8-sig')

print("\n================ 评估结果汇总 ================")
print(metrics_df)

# 打印满足条件的模型
ok = metrics_df[(metrics_df['MAPE(%)'] < 10) & (metrics_df['R²'] > 0.6)]
print("\n满足 MAPE<10% 且 R²>0.6 的模型:")
print(ok[['区域', '变量', '模型', 'MAPE(%)', 'R²']])
